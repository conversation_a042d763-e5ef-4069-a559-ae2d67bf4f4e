import bisect
from .object import TickData, StockData
from .utility import datatime_to_hms_int, time_string_to_hms_int

class CashTick:
    def __init__(self) -> None:
        self.timestamps = []   # 存储时间戳（如 93000, 93003, 93006...），已排序
        self.values: list[TickData] = []       # 对应的数据（价格、成交量等）
        self.ts_index: dict[int, int] = {}  # 时间戳到数据的映射
        self.index = -1
        self.limit_up_price = 0
        self.limit_down_price = 0
        self.limit_ratio = 0
        self.name = ""
        self.total_volume = 0
        self.float_volume = 0
        # 所有5档委托买入量
        self.bid_vol = 0
        # 所有5档委托卖出量
        self.ask_vol = 0
        # 涨停的索引 (买一价等于涨停价一样)
        self.up_stop_index = []
        # 卖一价等于涨停价的索引
        self.ask1_up_stop_index = []
        # 跌停的索引
        self.down_stop_index = []
        self.stop_status = 0 # 0: 1:涨停 -1：跌停  2: 卖一价等于涨停价(涨停前)
        # 昨日总成交笔数
        self.pre_today_transaction_num = 0
        # 昨日总成交量
        self.pre_today_volume_num = 0
    
    def add_tick(self, tick: TickData) -> None:
        """添加数据"""
        self.extra_tick(tick)
        self.timestamps.append(tick.timestamp)
        self.values.append(tick)
        self.ts_index[tick.timestamp] = self.index
        self.index += 1

    def extra_tick(self, tick: TickData) -> None:
        self.bid_vol += tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5
        self.ask_vol += tick.ask_volume_1 + tick.ask_volume_2 + tick.ask_volume_3 + tick.ask_volume_4 + tick.ask_volume_5
        if self.stop_status in (1, -1):
            if tick.ask_price_1 and tick.bid_price_1:
                self.stop_status = 0
        # 排除15：25之间的数据
        elif tick.volume:
            if not tick.ask_price_1:
                self.stop_status = 1
            elif not tick.bid_price_1:
                self.stop_status = -1
            elif round(tick.ask_price_1, 2) == round(tick.pre_close * self.limit_ratio, 2):
                self.stop_status = 2
        pre_tick: TickData = self.tick
        if not pre_tick:
            tick.net_amount = tick.amount
            tick.net_volume = tick.volume
            tick.max_bid_volume_1 = tick.bid_volume_1
            tick.max_ask_volume_1 = tick.ask_volume_1
            tick.max_bid_price_1 = tick.bid_price_1
            tick.max_ask_price_1 = tick.ask_price_1
            return
        tick.net_amount = tick.amount - pre_tick.amount
        tick.net_volume = tick.volume - pre_tick.volume
        tick.max_bid_volume_1 = max(tick.bid_volume_1, pre_tick.max_bid_volume_1)
        tick.max_ask_volume_1 = max(tick.ask_volume_1, pre_tick.max_ask_volume_1)
        tick.max_bid_price_1 = max(tick.bid_price_1, pre_tick.max_bid_price_1)
        tick.max_ask_price_1 = max(tick.ask_price_1, pre_tick.max_ask_price_1)
        if tick.last_price > pre_tick.last_price:
            tick.yang_volume += tick.net_volume
            tick.yang_amount += tick.net_amount
        elif tick.last_price < pre_tick.last_price:
            tick.yin_volume += tick.net_volume
            tick.yin_amount += tick.net_amount
    @property
    def tick(self):
        if not self.values:
            return None
        return self.values[-1]

    def get_exact_ts_index(self, timestamp: int):
        """精确查找"""
        return self.ts_index.get(timestamp, None)
    
    def get_ts_index(self, timestamps: int):
        # 找第一个 >= timestamps 的位置 索引
        index = self.get_exact_ts_index(timestamps)
        if index is not None:
            return index
        index = bisect.bisect_right(self.timestamps, timestamps)
        return index
    
    def get_index_tick(self, index: int):
        return self.values[index]

    def get_last_ticks_n(self, n: int):
        return self.values[-n:]

    def get_range_ticks_by_index(self, start_index: int, end_index: int):
        return self.values[start_index:end_index]

    def get_range_ticks(self, start_time: int, end_time: int):
        """查找某个时间的"""
        start_idx = self.get_ts_index(start_time)
        end_idx = self.get_ts_index(end_time)
        return self.values[start_idx:end_idx]

    def get_start_ticks(self, start_time: int, count: int):
        start_idx = self.get_ts_index(start_time)
        if count == -1:
            return self.values[start_idx:]
        else:
            return self.values[start_idx:start_idx+count]
    
    def get_end_ticks(self, end_time: int, count: int):
        end_idx = self.get_ts_index(end_time)
        return self.values[end_idx-count:end_idx]

    def get_tick_by_time(self, timestamp: int):
        index = self.get_ts_index(timestamp)
        return self.values[index]

    def get_ticks_by_price(self, price: float):
        """查找某个价格的tick"""
        ticks = [tick for tick in self.values if round(tick.last_price, 2) == round(price, 2)]
        return ticks
  
class CashManager:
    vt_symbols: dict[str, CashTick] = {}
    # 记录一些整体的情况
    base_info = {}
    def init(self) -> None:
        pass
    
    def update_base_info(self, data: dict):
        self.base_info.update(data)
        return self.base_info

    def init_cash_tick(self):
        pass

    