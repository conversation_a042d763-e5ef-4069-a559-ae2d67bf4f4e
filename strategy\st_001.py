from core.context import context_manager
from core.object import TickData
from core.cash_data import CashManager
from core.utility import datatime_to_hms_int
import time
import datetime
import traceback
from threading import Thread
from queue import Queue, Empty
from .base_strategy import BaseStrategy
import logging
logger = logging.getLogger(__name__)

class Strategy001(BaseStrategy):
    def __init__(self) -> None:
        super().__init__()

    def faa(self, vt_symbol: str, x:float, y: float):
        n = 5
        cash_tick = self.cash_ticks[vt_symbol]
        if cash_tick.up_stop_index:

        if cash_tick.index < n:
            return 0
        t0 , t1, t2, t3, t4 = cash_tick.values[n]
        is_flag = False
        if t1.last_price < t0.last_price and t1.last_price > t0.last_price:
            is_flag = True
        elif t1.last_price < t0.last_price and t2.last_price < t0.last_price and t3.last_price > t0.last_price:
            is_flag = True
        if is_flag:
            val = max([t1.net_volume, t2.net_volume, t3.net_volume, t4.net_volume]) / cash_tick.float_volume * 100
            amount = max([t1.net_amount, t2.net_amount, t3.net_amount, t4.net_amount]) / 10000 / 1000
            if val > x and amount > y:
                print(f"{vt_symbol}: 买入一份")
                return 1
        return 0

    def fbb(self, vt_symbol: str,n: int, x: float, y: float):
        cash_tick = self.cash_ticks[vt_symbol]


    def run(self):
        


        for thread in self.thread_lst:
            thread.start()














"""
AA：前4笔 
093000价如果小于092500价（92500为第一笔价），那么盘中第2笔价（一般是093003推送）要大于092500价，如果093000，093003都小于092500价，盘中第3笔价093006需要大于092500价,并且盘中第4笔价093009也需要大于092500价
（这里第几笔按照tick实际推送为准），同时max(093000，093003，093006, 093009)四笔的最大成交换手率要大于X%，四笔中的最大成交额大于Y千万，

   BB：第5笔-N分钟
如果此条件不能满足，则第5笔后至N分钟内的涨幅要大于开盘涨幅X%（或者比涨停价小于5档）并且期间出现单笔最大换手率大于Y%。（剔除前4笔的换手率）
条件满足出现后延时观察2个tick，计算比较是否同板块有优先个股触发，没有就立即成交一笔；有就放弃前者买入更优个股。
  
1笔>0笔，2笔>1笔，3笔>1笔，如果3笔<2笔价，判定第4笔要大于>3笔，否则进入N分钟模型
 CC：但是股价4笔内等于涨停价的算法，计算该股涨停价连续N笔的委托买一量的换手率要>=x,以第一等分额买入一笔。
5，.第一笔买入成交后， 如果后面N分钟之内股价回落开盘价下方X%，并且最高价和当时的回落价百分比小于Y%，该价格大于昨日最低价，同时所有阳量大于阴量继续成交一笔。
此前最高价必须大于开盘价。
6，买入个股总数不超过N支，买入个股数量参数，或者可用买入金额百分比，买入截止时间在X之前。
8，人工挂买未成交自动撤单条件，
AA：在指定时刻aabbcc个股没有出现再次封板就撤单。
BB：该笔挂买个股出现一笔成交量换手率大于X%，或者连续2笔换手率>y,或者委托买一量比最大买一量小于X%同时委托买一额小于X万元，自动撤单。





"""