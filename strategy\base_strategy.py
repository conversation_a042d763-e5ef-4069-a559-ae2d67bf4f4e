from core.context import context_manager
from core.object import TickData
from core.cash_data import CashManager
from core.utility import datatime_to_hms_int
import time
import datetime
import traceback
from threading import Thread
from queue import Queue, Empty
import logging
logger = logging.getLogger(__name__)

class BaseStrategy:
    def __init__(self) -> None:
        self.queue: Queue = Queue()
        self.name = "base_strategy"
        self.active = False
       
        self.geteway_api = context_manager.module_obj("geteway")
        self.thread_lst: list[Thread] = [
            Thread(target=self.on_tick),
        ]
        self.n = 1
        self.tick_dct = {}
        self.pre_day_data = {}
        self.cash = CashManager()
        self.cash_ticks = self.cash.vt_symbols
    
    def gen_today_ts(self, hour: int, minute: int, second: int):
        df = datetime.datetime.now().replace(hour=hour, minute=minute, second=second, microsecond=0)
        timestrap = df.timestamp() * 1000
        return timestrap
    
    def cover_str_to_ts(self, time_str: str):
        # time_str 093000
        hour = int(time_str[:2])
        minute = int(time_str[2:4])
        second = int(time_str[4:6])
        return self.gen_today_ts(hour, minute, second)

    def f01(self, vt_symbol: str):
        tick: TickData = self.cash_ticks[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3)*tick.bid_price_1 / 10000
        return round(val, 2)

    def f01b(self, vt_symbol: str):
        tick: TickData = self.cash_ticks[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3) * \
        ((tick.bid_price_1 + tick.bid_price_2 + tick.bid_price_3) / 3) / 10000
        return round(val, 2)

    def f02(self, vt_symbol: str):
        tick: TickData = self.cash_ticks[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) * tick.bid_price_1 / 10000
        return round(val, 2)

    def f03(self, vt_symbol: str):
        tick: TickData = self.cash_ticks[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3) / (tick.bid_volume_1 + tick.bid_volume_2)
        return round(val, 2)

    def f04(self, vt_symbol: str):
        tick: TickData = self.cash_ticks[vt_symbol].tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / (tick.bid_volume_1 + tick.bid_volume_2)
        return round(val, 2)

    def f05(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3) / cash_tick.float_volume *100
        return round(val, 2)

    def f06(self,  vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2) / tick.ask_volume_1
        return round(val, 2)

    def f06b(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2) / (tick.bid_volume_1)
        return round(val, 2)

    def f07(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / (tick.ask_volume_1 + tick.ask_volume_2 + tick.ask_volume_3 + tick.ask_volume_4 + tick.ask_volume_5)
        return round(val, 2)

    def f08(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / (tick.ask_volume_1 + tick.ask_volume_2 + tick.ask_volume_3 + tick.ask_volume_4 + tick.ask_volume_5)
        return round(val, 2)

    def f09(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.net_volume / cash_tick.float_volume *100
        return round(val, 2)
    
    def f10(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.volume / cash_tick.float_volume * 100
        return round(val, 2)

    def f11(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.net_amount
        return round(val, 2)

    def f12(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.net_amount
        return round(val, 2)

    def f13(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.bid_volume_1 / cash_tick.float_volume * 100
        return round(val, 2)

    def f13b(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.max_bid_volume_1 / cash_tick.float_volume * 100
        return round(val, 2)

    def f14(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.ask_volume_1 / cash_tick.float_volume * 100
        return round(val, 2)

    def f14b(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.max_ask_volume_1 / cash_tick.float_volume * 100
        return round(val, 2)

    def f15(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5
        return round(val, 2)

    def f15b(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_volume_1 + tick.bid_volume_2 + tick.bid_volume_3 + tick.bid_volume_4 + tick.bid_volume_5) / cash_tick.float_volume * 100
        return round(val, 2)
    
    def f16a(self, vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)

        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.ask_price_1:
                val += tick.net_volume
            pre_tick = tick
        return val
    
    def f16c(self, vt_symbol: str, start_time: str, end_time: str, n=1):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > getattr(pre_tick, f"ask_volume_{n}"):
                val += tick.net_volume
                pre_tick = tick
        return val
    
    def f16b(self,vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.bid_price_1:
                val += tick.net_volume
            pre_tick = tick
        return val
    
    def f16d(self, vt_symbol: str, start_time: str, end_time: str, n=1):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < getattr(pre_tick, f"bid_volume_{n}"):
                val += tick.net_volume
                pre_tick = tick
        return val

    def f17(self, vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
                val += tick.net_amount
            pre_tick = tick
        return round(val/10000, 2)
    
    def f18(self, vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
                val += tick.net_amount
            pre_tick = tick
        return round(val/10000, 2)

    def f19(self, vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
                val += tick.net_volume
            pre_tick = tick
        return val

    def f19a(self, vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price == pre_tick.last_price:
                val += tick.net_volume
            pre_tick = tick
        return val

    def f20(self, vt_symbol: str, start_time: str, end_time: str, n: float):
        # n = 2 代表2%
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
               if tick.net_volume / cash_tick.float_volume * 100 > n: 
                    val += tick.net_volume
            pre_tick = tick
        return val
    def f20b(self, vt_symbol: str, start_time: str, end_time: str, n: float):
        # n = 2 代表2%
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val

        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
               if tick.net_volume / cash_tick.float_volume * 100 > n:
                    val += tick.net_volume
            pre_tick = tick
        return val

    def f21(self, vt_symbol: str, start_time: str, end_time: str, n: float):
        # 金额 单位万
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val

        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
               if tick.net_amount / 10000 > n:
                    val += tick.net_amount
            pre_tick = tick
        return val

    def f21b(self, vt_symbol: str, start_time: str, end_time: str, n: float):
        # 金额 单位万
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val

        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
               if tick.net_amount / 10000 > n: 
                    val += tick.net_amount
            pre_tick = tick
        return val
    def f22(self, vt_symbol, start_time: str, end_time: str):
        # 金额 单位万
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        
        tmp_lst = []
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
                tmp_lst.append(tick.net_volume)
            pre_tick = tick
        val = max(tmp_lst) / cash_tick.float_volume * 100
        return round(val, 2)
    
    def f22b(self, vt_symbol, start_time: str, end_time: str):
        # 金额 单位万
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        tmp_lst = []
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
                tmp_lst.append(tick.net_amount)
            pre_tick = tick
        val = max(tmp_lst) / 10000
        return round(val, 2)
    
    # qmt 但是获取不到成交笔数的值 暂时不可用
    def f23(self, vt_symbol: str, start_time: str, end_time: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        
        tmp_lst = [tick.transaction_num for tick in ticks if tick.net_volume < n]
        val = sum(tmp_lst)
        return val
    # qmt 但是获取不到成交笔数的值 暂时不可用
    def f24(self, vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price > pre_tick.last_price:
                val += tick.transaction_num
            pre_tick = tick
        return val
    # qmt 但是获取不到成交笔数的值 暂时不可用
    def f25(self, vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        val = 0
        if not ticks:
            return val
        pre_tick = ticks[0]
        for tick in ticks[1:]:
            if tick.last_price < pre_tick.last_price:
                val += tick.transaction_num
            pre_tick = tick
        return val

    # qmt 但是获取不到成交笔数的值 暂时不可用
    def f26(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.transaction_num
        return val

    # qmt 但是获取不到成交笔数的值 暂时不可用 除非从别的地方提前获取
    def f27(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = cash_tick.pre_today_transaction_num
        return val

    def f28(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = cash_tick.pre_today_volume_num
        return val
    
    # 指标29：前池a指定时刻买一价=涨停价总数
    # 指标30:昨日最大一字连板天数个股
    # 指标30A，昨日一字板
    # 指标30B，前一日一字板
    # 指标30C，昨日一字板前日最高价小于前日涨停价
    # 指标30D，一字起板：第一个涨停板为一字板的个股。

    def f31(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.bid_volume_1 / cash_tick.float_volume * 100
        return round(val, 2)

    def f32(self, vt_symbol: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_last_ticks_n(n)
        val = min([t.bid_volume_1 for t in ticks]) / cash_tick.float_volume * 100
        return round(val, 2)

    def f33(self, vt_symbol: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_last_ticks_n(n)
        val = max([t.bid_volume_1 for t in ticks]) / cash_tick.float_volume * 100
        return round(val, 2)

    def f34(self, vt_symbol: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_last_ticks_n(n)
        val = (min([t.bid_volume_1 for t in ticks]) / cash_tick.tick.open - 1) * 100
        return val

    def f35(self, vt_symbol: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_last_ticks_n(n)
        val = (max([t.bid_volume_1 for t in ticks]) / cash_tick.tick.open - 1) * 100
        return round(val, 2)

    def f36(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_price_1 / tick.low - 1) * 100
        return round(val, 2)

    def f37(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.bid_price_1 / tick.high - 1) * 100
        return round(val, 2)

    def f37b(self, vt_symbol: str, time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        timestamp = self.cover_str_to_ts(time)
        tick = cash_tick.get_tick_by_time(timestamp)
        val = (tick.bid_price_1 / tick.high - 1) * 100
        return round(val, 2)
    
    def f37c(self, vt_symbol: str, time: str, start_time="093000"):
        cash_tick = self.cash_ticks[vt_symbol]
        end_time = self.cover_str_to_ts(time)
        start_time = self.cover_str_to_ts(start_time)
        ticks  = cash_tick.get_range_ticks(start_time, end_time)
        val = (max([t.bid_price_1 for t in ticks]) / cash_tick.tick.open - 1) *100
        return round(val, 2)
    
    def f38(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = (tick.open / tick.low - 1 )*100
        return round(val, 2)
    
    def f39(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.amount / tick.volume
        return round(val, 2)
    
    def f40(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.yang_amount / tick.yang_volume
        return round(val, 2)
    
    def f41(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        tick: TickData = cash_tick.tick
        val = tick.yin_amount / tick.yin_volume
        return round(val, 2)
    
    def f42(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.up_stop_index:
            return None
        index = cash_tick.up_stop_index[0]
        tick = cash_tick.get_index_tick(index)
        return tick.timestamp
    
    def f43(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.ask1_up_stop_index:
            return None
        index = cash_tick.ask1_up_stop_index[0]
        tick = cash_tick.get_index_tick(index)
        return tick.timestamp
    
    def f44(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.up_stop_index:
            return None
        pre_idx = None
        for idx in cash_tick.up_stop_index:
            if pre_idx is None:
                pre_idx = idx
                continue
            if idx - pre_idx > 1:
                tick = cash_tick.get_index_tick(idx)
                return tick.timestamp
            pre_idx = idx
        return None
    
    def f45(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.up_stop_index:
            return None
        pre_idx = None
        zha_idx = 0
        for idx in cash_tick.up_stop_index:
            if pre_idx is None:
                pre_idx = idx
                continue
            if idx - pre_idx > 1:
                zha_idx = idx
                break
            pre_idx = idx
        for idx in cash_tick.ask1_up_stop_index:
            if idx > zha_idx:
                tick = cash_tick.get_index_tick(idx)
                return tick.timestamp
        return None
    
    # 指标45+，二次封板时刻的买一量，连续N笔的买一量
    def f45b(self, vt_symbol: str, n : int=1):
        cash_tick = self.cash_ticks[vt_symbol]
        val = 0
        if not cash_tick.up_stop_index:
            return val
        pre_idx = None
        stop_cnt = 0
        for idx in cash_tick.up_stop_index:
            if pre_idx is None:
                pre_idx = idx
                stop_cnt += 1
                continue
            if idx - pre_idx > 1:
                stop_cnt += 1
            if stop_cnt == 2:
                ticks = cash_tick.get_range_ticks_by_index(idx -n, idx)
                val = sum([t.bid_volume_1 for t in ticks])
                return val
        return val
    # 指标45+，二次封板时刻的成交量，连续N笔的成交量
    def f45c(self, vt_symbol: str, n : int=1):
        cash_tick = self.cash_ticks[vt_symbol]
        val = 0
        if not cash_tick.up_stop_index:
            return val
        pre_idx = None
        stop_cnt = 0
        for idx in cash_tick.up_stop_index:
            if pre_idx is None:
                pre_idx = idx
                stop_cnt += 1
                continue
            if idx - pre_idx > 1:
                stop_cnt += 1
            if stop_cnt == 2:
                tick_1 = cash_tick.get_index_tick(idx)
                tick_2 = cash_tick.get_index_tick(idx - n)
                val = tick_1.volume - tick_2.volume
                return val
        return val
    
    def f46(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.up_stop_index:
            return 0
        tick = cash_tick.get_index_tick(cash_tick.up_stop_index[0])
        return tick.low
    
    def f47(self, vt_symbol: str):
        timestamp = self.f45(vt_symbol)
        if not timestamp:
            return 0
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_start_ticks(timestamp, -1)
        val = min([t.last_price for t in ticks])
        return round(val, 2)
    
    def f47b(self, vt_symbol: str):
        low = self.f47(vt_symbol)
        if not low:
            return 0
        cash_tick = self.cash_ticks[vt_symbol]
        tick = cash_tick.get_index_tick(cash_tick.up_stop_index[0])
        val = (low / tick.low) * 100
        return round(val, 2)
    
    def f48a(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = 0
        if not cash_tick.up_stop_index:
            return val
        for idx in cash_tick.up_stop_index:
            tick = cash_tick.get_index_tick(idx)
            val += tick.net_volume
        return val
    
    # 指标48D，涨停板成交量/流通盘*100
    def f48b(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = self.f48a(vt_symbol) / cash_tick.float_volume * 100
        return round(val, 2)
    
    # 指标48A，涨停封单率：     这个是涨停那一刻？
    def f48c(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = 0
        if not cash_tick.up_stop_index:
            return val
        tick = cash_tick.get_index_tick(cash_tick.up_stop_index[0])
        val = tick.bid_volume_1 / cash_tick.float_volume * 100
        return round(val, 2)
    
    # 指标48B，统计买一价=涨停价所有的买一量的和
    def f48d(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = 0
        if not cash_tick.up_stop_index:
            return val
        for idx in cash_tick.up_stop_index:
            tick = cash_tick.get_index_tick(idx)
            val += tick.bid_volume_1
        return val

    # 指标48C，撤单率：当前的委买1量减上1笔的委买1量减当前时刻的成交量／流通盘
    def f48e(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        pre_tick, tick = cash_tick.get_last_ticks_n(2)
        val = (tick.bid_volume_1 - pre_tick.bid_volume_1 - tick.net_volume) / cash_tick.float_volume * 100
        return round(val, 2)
    
    # 如果当前不是涨停那 ？  成交量 是开盘以来  ？
    def f49(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = 0
        if not cash_tick.up_stop_index:
            return val
        idx = cash_tick.up_stop_index[0]
        ticks =  cash_tick.get_range_ticks_by_index(idx, -1)
        val = sum([tick.bid_price_1 for tick in ticks]) / ticks[0].volume
        return round(val, 2)

    def f50(self, vt_symbol: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_last_ticks_n(n)
        val  = (ticks[-1].volume - ticks[0].volume) / ticks[-1].bid_volume_1
        return round(val, 2)

    def f51(self, vt_symbol: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_last_ticks_n(n)
        val  = (ticks[-1].volume - ticks[0].volume) / cash_tick.float_volume * 100
        return round(val, 2)

    def f52(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = 0
        if cash_tick.up_stop_index:
            return val
        start_idx = 0
        end_idx = 0
        pre_idx = None
        for idx in cash_tick.up_stop_index:
            if pre_idx is None:
                pre_idx = idx
                continue
            if start_idx:
                end_idx = idx
                break
            if not start_idx and idx - pre_idx > 1:
                start_idx = idx
        if start_idx and end_idx:
            start_tick = cash_tick.get_index_tick(start_idx)
            end_tick = cash_tick.get_index_tick(end_idx)
            val = (end_tick.yang_volume - start_tick.yang_volume) - (end_tick.yin_volume - start_tick.yin_volume)
        return val

    def f53(self, vt_symbol: str, time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        timestamp = self.cover_str_to_ts(time)
        tick = cash_tick.get_tick_by_time(timestamp)
        pre_tick = cash_tick.get_index_tick(tick.index - 1)
        val = tick.net_volume / pre_tick.bid_volume_1
        return round(val, 2)
    # 指标54，指定时刻的成交量/该tick后的第N笔的最大成交量     最大成交量  指定是  ？
    def f54(self, vt_symbol: str, time: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        timestamp = self.cover_str_to_ts(time)
        tick = cash_tick.get_tick_by_time(timestamp)
        end_idx = tick.index + n
        val = 0
        if cash_tick.index < end_idx:
            return val
        ticks = cash_tick.get_range_ticks_by_index(tick.index, end_idx)
        val = tick.net_volume / max([t.net_volume for t in ticks])
        return round(val, 2)

    # 指标55，指定时刻的成交价/该tick后的第N笔的最大成交价  同上？ 这个区间?
    def f55(self, vt_symbol: str, time: str, n: int):
        cash_tick = self.cash_ticks[vt_symbol]
        timestamp = self.cover_str_to_ts(time)
        tick = cash_tick.get_tick_by_time(timestamp)
        end_idx = tick.index + n
        val = 0
        if cash_tick.index < end_idx:
            return val
        ticks = cash_tick.get_range_ticks_by_index(tick.index, end_idx)
        val = tick.last_price / max([t.last_price for t in ticks])
        return round(val, 2)
    # 指标57

    def f57b(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = cash_tick.tick.open
        return round(val, 2)
    def f58(self, vt_symbol: str, price: float):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_ticks_by_price(price)
        val = max([t.net_volume for t in ticks])

        return val
    
    # 指标59
    def f60(self, vt_symbol: str, price: float):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_ticks_by_price(price)
        val = max([t.bid_volume_1 for t in ticks])
        return val

    def f61(self, vt_symbol: str, price: float):
        cash_tick = self.cash_ticks[vt_symbol]
        ticks = cash_tick.get_ticks_by_price(price)
        # val = max([t.bid_volume_1 for t in ticks])
        # return val

    def f66(self, vt_symbol: str):
        val = 0
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.up_stop_index:
            return val
        pre_idx = None
        for idx in cash_tick.up_stop_index:
            if pre_idx is None:
                pre_idx = idx
                continue
            if idx - pre_idx > 1:
                val += 1
            pre_idx = idx
        if cash_tick.tick.ask_price_1:
            val  += 1
        return val
        


    def f67(self, vt_symbol: str, time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        timestamp = self.cover_str_to_ts(time)
        tick = cash_tick.get_tick_by_time(timestamp)
        # 涨跌幅
        val = (tick.last_price - tick.pre_close) / tick.pre_close * 100
        
        # 振幅
        # val = (tick.high - tick.low) / tick.pre_close * 100
        return round(val, 2)
    
    def f70(self, vt_symbol: str, start_time: str, end_time: str):
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        volume = sum([tick.net_volume for tick in ticks[1:] if tick.last_price >= ticks[tick.index - 1].last_price])
        val = volume / cash_tick.float_volume * 100
        return volume(val, 2)
    
    def f71(self, vt_symbol: str, start_time: str, end_time: str, n: int):
        # >N（单位万）
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        amount = sum([tick.net_amount for tick in ticks[1:] if tick.last_price >= ticks[tick.index - 1].last_price])
        val = bool(amount / 10000 > n)
        return val
    
    def f72(self, vt_symbol: str, start_time: str, end_time: str, n: int):
        # >N（单位万）
        cash_tick = self.cash_ticks[vt_symbol]
        start_time = self.cover_str_to_ts(start_time)
        end_time = self.cover_str_to_ts(end_time)
        ticks = cash_tick.get_range_ticks(start_time, end_time)
        amount = sum([tick.net_amount for tick in ticks[1:] if tick.last_price < ticks[tick.index - 1].last_price])
        val = bool(amount / 10000 > n)
        return val
    def f73(self, vt_symbol: str, n: int):
        # >N（单位万）
        cash_tick = self.cash_ticks[vt_symbol]
        for tick in cash_tick.values:
            if (tick.last_price / tick.open - 1) * 100 > n:
                return tick.timestamp
        return 0
    
    def f74(self, vt_symbols: list[str], func):
        tmp_lst = [{vt_symbol: func(vt_symbol)} for vt_symbol in vt_symbols]
        # 从大到小排序
        tmp_lst.sort(key=lambda x: list(x.values())[0], reverse=True)
        return tmp_lst
    

    def f79(self, vt_symbol: str, price: float):
        cash_tick = self.cash_ticks[vt_symbol]
        for tick in cash_tick.values:
            if round(tick.bid_price_1, 2) == round(price, 2):
                val = tick.bid_volume_1 * tick.bid_price_1 / 10000
                return round(val, 2)
        return 0

    def f80(self, vt_symbol: str, price: float):
        cash_tick = self.cash_ticks[vt_symbol]
        for tick in cash_tick.values:
            if round(tick.bid_price_1, 2) == round(price, 2):
                val = tick.bid_volume_1 / cash_tick.float_volume * 100
                return round(val, 2)
        return 0

    def f82(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = (cash_tick.bid_vol - cash_tick.ask_vol) / cash_tick.ask_vol
        return round(val, 2)
    def f83(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        val = (cash_tick.tick.volume - cash_tick.ask_vol) / cash_tick.ask_vol
        return round(val, 2)

    # 指标84，昨日成交量最大的单笔tick对应的成交价  这得提前记录

    def f85d(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.down_stop_index:
            return 0
        val = max([cash_tick.get_index_tick(idx).ask_volume_1 for idx in cash_tick.down_stop_index])
        return val

    def f85f(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.down_stop_index:
            return 0
        val = sum([cash_tick.get_index_tick(idx).net_volume for idx in cash_tick.down_stop_index])
        return val
    def f85g(self, vt_symbol: str):
        cash_tick = self.cash_ticks[vt_symbol]
        if not cash_tick.down_stop_index:
            return 0
        # val = sum([cash_tick.get_index_tick(idx).net_volume for idx in cash_tick.down_stop_index])
        # return val
    def f86(self):
        vt_symbol = "1A0001.SH"
        cash_tick = self.cash_ticks[vt_symbol]
        return cash_tick.tick.last_price
  

