from xtquant import xtdata
markets: list = [
    "沪深A股",
    "沪深转债",
    "沪深ETF",
    "沪深指数",
    "京市A股"
]
xtdata.download_sector_data()
sector_list = xtdata.get_sector_list()
for x in sector_list:
    if "GN" in x:
        print(x)
        xt_symbols: list = xtdata.get_stock_list_in_sector(x)
        print(xt_symbols[:10])
        break

# for i in markets:
#     xt_symbols: list = xtdata.get_stock_list_in_sector(i)